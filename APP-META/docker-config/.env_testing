SERVER_PORT=5002
SERVER_KEY=lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi
GIN_MODE=release
PLATFORM=local

DIFY_INNER_API_KEY=QaHbTe77CtuXmsfyhR7+vRjI/+XbV1AaFy691iy+kGDv2Jvy0/eAh8Y1
DIFY_INNER_API_URL=http://1d-workflow-daily-api.alibaba.net

PLUGIN_REMOTE_INSTALLING_ENABLED=false
PLUGIN_REMOTE_INSTALLING_HOST=0.0.0.0
PLUGIN_REMOTE_INSTALLING_PORT=9999

# s3 credentials
S3_USE_AWS_MANAGED_IAM=true
S3_ENDPOINT=
S3_USE_PATH_STYLE=true
AWS_ACCESS_KEY=
AWS_SECRET_KEY=
AWS_REGION=

# tencent cos credentials
TENCENT_COS_SECRET_KEY=
TENCENT_COS_SECRET_ID=
TENCENT_COS_REGION=

# services storage
PLUGIN_STORAGE_TYPE=aliyun_oss
PLUGIN_STORAGE_OSS_BUCKET=oneday-workflow-plugin-test
PLUGIN_STORAGE_LOCAL_ROOT=./storage

# aliyun oss credentials
ALIYUN_OSS_REGION=cn-zhangjiakou
ALIYUN_OSS_ENDPOINT=https://oss-cn-zhangjiakou-internal.aliyuncs.com
ALIYUN_OSS_ACCESS_KEY_ID=LTAI5tHJ8cS1e5mGqu8KE3YX
ALIYUN_OSS_ACCESS_KEY_SECRET=******************************
ALIYUN_OSS_AUTH_VERSION=v4
ALIYUN_OSS_PATH=/

# where the plugin finally installed
PLUGIN_INSTALLED_PATH=plugin

# where the plugin finally running and working
PLUGIN_WORKING_PATH=cwd

# persistence storage
PERSISTENCE_STORAGE_PATH=persistence
PERSISTENCE_STORAGE_MAX_SIZE=104857600

# plugin webhook
PLUGIN_WEBHOOK_ENABLED=true

# routine pool
ROUTINE_POOL_SIZE=1024

# redis
REDIS_HOST=r-8vb4e4dfb8dea674.redis.zhangbei.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_USERNAME=onedayworkflow_test
REDIS_PASSWORD=yNL3A_fRj4!E_Q6
REDIS_USE_SSL=false
REDIS_DB=0

DB_USERNAME=oneday_admin
DB_PASSWORD=9MBmVimp7Cjs3
DB_HOST=mr-fmzrhvrqnnllw2jnvr.rwlb.zhangbei.rds.aliyuncs.com
DB_PORT=3432
DB_DATABASE=oneday-workflow-test

DIFY_INVOCATION_CONNECTION_IDLE_TIMEOUT=120

MAX_PLUGIN_PACKAGE_SIZE=52428800

# dify serverless connector
DIFY_PLUGIN_SERVERLESS_CONNECTOR_URL=http://127.0.0.1:5004
DIFY_PLUGIN_SERVERLESS_CONNECTOR_API_KEY=HeRFb6yrzAy5vUSlJWK2lUl36mpkaRycv4witbQpucXacgXg7G9a8gVL

# python interpreter, if you are using local runtime, you should set this path to your python interpreter path
# otherwise, it should be /usr/bin/python3
# PYTHON_INTERPRETER_PATH=/usr/bin/python3

# python environment init timeout, if the python environment init process is not finished within this time, it will be killed
PYTHON_ENV_INIT_TIMEOUT=120

# pprof enabled, for debugging
PPROF_ENABLED=false

# FORCE_VERIFYING_SIGNATURE, for security, you should set this to true, pls be sure you know what you are doing
# if want to install plugin without verifying signature, set this to false
FORCE_VERIFYING_SIGNATURE=true

# proxy settings, example: HTTP_PROXY=http://host.docker.internal:7890
HTTP_PROXY=
HTTPS_PROXY=

# PIP
PIP_MIRROR_URL=http://artlab.alibaba-inc.com/1/pypi/simple

DEPLOY_ENV=DEVELOPMENT
