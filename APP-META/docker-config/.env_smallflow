SERVER_PORT=5002
SERVER_KEY=lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi
GIN_MODE=release
PLATFORM=local

DIFY_INNER_API_KEY=QaHbTe77CtuXmsfyhR7+vRjI/+XbV1AaFy691iy+kGDv2Jvy0/eAh8Y1
DIFY_INNER_API_URL=http://spe-oneday-workflow-api.alibaba-inc.com

PLUGIN_REMOTE_INSTALLING_ENABLED=false
PLUGIN_REMOTE_INSTALLING_HOST=0.0.0.0
PLUGIN_REMOTE_INSTALLING_PORT=9999

# s3 credentials
S3_USE_AWS_MANAGED_IAM=true


# redis
REDIS_HOST=r-8vb7ae51362ef3f4.redis.zhangbei.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_USERNAME=onedayworkflow_redis_prod
REDIS_PASSWORD=yNL3AfRj4EQ6
REDIS_USE_SSL=false
REDIS_DB=0

DB_USERNAME=oneday_workflow_prod_admin
DB_PASSWORD=e3g-Xmhmskc7Y2!
DB_HOST=mr-y72hc7ouy3v9j9my0f.rwlb.zhangbei.rds.aliyuncs.com
DB_PORT=3432
DB_DATABASE=oneday-workflow-db-prod

# services storage
PLUGIN_STORAGE_TYPE=aliyun_oss
PLUGIN_STORAGE_OSS_BUCKET=oneday-workflow-plugin-test
PLUGIN_STORAGE_LOCAL_ROOT=/home/<USER>/oneday-workflow-plugin-daemon/storage

# aliyun oss credentials
ALIYUN_OSS_REGION=cn-zhangjiakou
ALIYUN_OSS_ENDPOINT=https://oss-cn-zhangjiakou-internal.aliyuncs.com
ALIYUN_OSS_ACCESS_KEY_ID=LTAI5tHJ8cS1e5mGqu8KE3YX
ALIYUN_OSS_ACCESS_KEY_SECRET=******************************
ALIYUN_OSS_AUTH_VERSION=v4
ALIYUN_OSS_PATH=/

# where the plugin finally installed
PLUGIN_INSTALLED_PATH=plugin

# where the plugin finally running and working
PLUGIN_WORKING_PATH=cwd

PLUGIN_PACKAGE_CACHE_PATH=plugin_packages
# persistence storage
PERSISTENCE_STORAGE_PATH=persistence
PERSISTENCE_STORAGE_MAX_SIZE=104857600

# plugin webhook
PLUGIN_WEBHOOK_ENABLED=true

# routine pool
ROUTINE_POOL_SIZE=1024


DIFY_INVOCATION_CONNECTION_IDLE_TIMEOUT=1200

MAX_PLUGIN_PACKAGE_SIZE=52428800

# dify serverless connector
DIFY_PLUGIN_SERVERLESS_CONNECTOR_URL=http://127.0.0.1:5004
DIFY_PLUGIN_SERVERLESS_CONNECTOR_API_KEY=HeRFb6yrzAy5vUSlJWK2lUl36mpkaRycv4witbQpucXacgXg7G9a8gVL

# python interpreter, if you are using local runtime, you should set this path to your python interpreter path
# otherwise, it should be /usr/bin/python3
PYTHON_INTERPRETER_PATH=/usr/bin/python3

# python environment init timeout, if the python environment init process is not finished within this time, it will be killed
PYTHON_ENV_INIT_TIMEOUT=5000

# pprof enabled, for debugging
PPROF_ENABLED=false

# FORCE_VERIFYING_SIGNATURE, for security, you should set this to true, pls be sure you know what you are doing
# if want to install plugin without verifying signature, set this to false
FORCE_VERIFYING_SIGNATURE=false

# Enable or disable third-party signature verification for plugins
# Set to "true" to allow verification using additional public keys specified in THIRD_PARTY_SIGNATURE_VERIFICATION_PUBLIC_KEYS
THIRD_PARTY_SIGNATURE_VERIFICATION_ENABLED=false

PIP_MIRROR_URL=http://artlab.alibaba-inc.com/1/pypi/simple

REMOTE_INSTALL_URL=127.0.0.1:0000

DEPLOY_ENV=PRODUCTION